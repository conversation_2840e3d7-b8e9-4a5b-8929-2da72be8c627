import app from './app';
import http from 'http';
import { IncludeModelAssociations, validateEnvironment, validateSessionConfig } from './global/helpers';
import { Sequelize, Var } from './global/var';
import { redisClient } from './global/services';
import dotenv from 'dotenv';
dotenv.config();

// SECURITY HARDENING: Validate environment before starting
validateEnvironment();
validateSessionConfig();

(async () => {
  try {
    await new Promise<void>((resolve, reject) => {
      redisClient.on('ready', () => {
        console.log(`${Var.app.emoji.success} Redis client connected`);
        resolve();
      });
      redisClient.on('error', (err) => {
        console.error(`${Var.app.emoji.failure} Could not connect to Redis:`, err);
        reject(err);
      });
    });
  } catch (err) {
    console.log(`${Var.app.emoji.failure} Exiting due to Redis connection failure.`);
    process.exit(1);
  }

  try {
    await Sequelize.authenticate();
    console.log(`${Var.app.emoji.success} Database authenticated`);
  } catch (err) {
    console.log(`${Var.app.emoji.failure} Could not authenticate database`);
    console.log(err);
    process.exit(1);
  }

  IncludeModelAssociations();

  // Sync database models
  try {
    if (Var.node.env === 'dev' && Var.node.db.reset) {
      console.log(`${Var.app.emoji.warning} Forcing database reset as requested by RESET_DB=true...`);
      await Sequelize.sync({ force: true });
      console.log(`${Var.app.emoji.success} Database reset and tables created`);
    }
    else if (Var.node.env === 'dev' && Var.node.db.alter) {
      console.log(`${Var.app.emoji.warning} Altering database tables as requested by ALTER_DB=true...`);
      await Sequelize.sync({ alter: true });
      console.log(`${Var.app.emoji.success} Database tables altered`);
    }
    else {
      console.log(`${Var.app.emoji.info} Syncing database models without dropping or altering tables...`);
      await Sequelize.sync({ force: false, alter: false });
      console.log(`${Var.app.emoji.success} Database tables synced (tables created if they didn't exist)`);
    }
  } catch (err) {
    console.error(err);
    console.log(`${Var.app.emoji.failure} Could not sync models`);
  }

  const nodeServer = http.createServer(app);

  nodeServer.listen(Var.node.port, () => {
    console.log(`${Var.app.emoji.success} Server is running on port: ${Var.node.port}`);
  });
})();
