import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';

export const BlockRequestByOrigin = async (_req: Request, res: Response, next: NextFunction) => {
  if (Var.node.env === 'prod') {
    if (!res.locals.origin) {
      console.log(`${Var.app.emoji.failure} No origin provided in production environment`);
      return res.status(200).json({
        success: false,
        message: `${Var.app.emoji.failure} You are not authorized to make this request`,
      });
    }

    if (res.locals.origin !== Var.app.url.prod) {
      console.log(`${Var.app.emoji.failure} ${res.locals.origin} is not an authorized origin`);
      return res.status(200).json({
        success: false,
        message: `${Var.app.emoji.failure} You are not authorized to make this request`,
      });
    }
  } else {
    if (!res.locals.origin) {
      console.log(`${Var.app.emoji.warning} Request with no origin allowed in development environment`);
    }
  }
  console.log(`${Var.app.emoji.success} ${res.locals.origin || 'No origin (development)'} is authorized origin`);
  next();
};
