import { DataTypes, ModelAttributes, ModelOptions } from 'sequelize';
import { Sequelize } from '../../../../global/var';

const modelName: string = 'account';

const modelAttributes: ModelAttributes = {
  meta: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
  },
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    allowNull: false,
    unique: true,
    primaryKey: true,
  },
  name: { type: DataTypes.STRING, allowNull: false },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: { isEmail: true },
  },
  password: { type: DataTypes.STRING, allowNull: true },
  is_email_verified: { type: DataTypes.BOOLEAN, defaultValue: false },
  email_verification_code: {
    type: DataTypes.STRING,
  },
  email_verification_code_timestamp: {
    type: DataTypes.DATE,
  },
  password_reset_code: {
    type: DataTypes.STRING,
  },
  password_reset_code_timestamp: {
    type: DataTypes.DATE,
  },
  is_google_oauth_linked: { type: DataTypes.BOOLEAN, defaultValue: false },
  is_locked: { type: DataTypes.BOOLEAN, defaultValue: false },
  lock_expires_at: { type: DataTypes.DATE, allowNull: true },
  last_login_at: { type: DataTypes.DATE, allowNull: true },
  login_attempts: { type: DataTypes.INTEGER, defaultValue: 0 },
};

const modelOptions: ModelOptions = {
  tableName: 'accounts',
  underscored: true,
  freezeTableName: true,
};

export const accountModel = Sequelize.define(modelName, modelAttributes, modelOptions);
