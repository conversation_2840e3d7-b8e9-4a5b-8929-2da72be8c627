import { hash, argon2id } from 'argon2';
import { logger } from '../../../../global/services';

export const hashPassword = async (password: string): Promise<string> => {
  try {
    const hashedPassword: string = await hash(password, {
      type: argon2id,
      memoryCost: 65536,
      timeCost: 3,
      parallelism: 4,
      saltLength: 32,
      hashLength: 32,
    });

    return hashedPassword;
  } catch (error) {
    logger.error('Error hashing password', { error });
    throw new Error('Failed to hash password');
  }
};
