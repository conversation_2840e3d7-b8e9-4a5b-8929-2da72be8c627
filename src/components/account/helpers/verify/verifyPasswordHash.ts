import * as argon from 'argon2';
import { logger } from '../../../../global/services';

export const verifyPasswordHash = async (hashedPassword: string, plainPassword: string): Promise<boolean> => {
  try {
    const isPasswordValid: boolean = await argon.verify(hashedPassword, plainPassword);
    return isPasswordValid;
  } catch (error) {
    logger.error('Error verifying password', { error });
    return false;
  }
};
