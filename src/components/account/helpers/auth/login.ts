import { Request, Response } from 'express';
import { logger } from '../../../../global/services';
import { writeAccountLockStatus } from '../../dals/write/writeAccountLockStatus';
import { accountModel } from '../../models';

export const login = async (req: Request, _res: Response, accountId: string) => {
  try {
    req.session!.accountId = accountId;
    req.session!.createdAt = Date.now();
    req.session!.userAgent = req.headers['user-agent'];
    req.session!.ipAddress = req.ip;

    await updateLastLoginTimestamp(accountId);

    await writeAccountLockStatus(accountId, false, null);

    logger.info(`User logged in successfully`, {
      accountId,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
  } catch (error) {
    logger.error(`Error in login helper`, { accountId, error });
  }
};

async function updateLastLoginTimestamp(accountId: string): Promise<void> {
  try {
    await accountModel.update(
      {
        last_login_at: new Date(),
        login_attempts: 0,
      },
      {
        where: { id: accountId },
      },
    );
  } catch (error) {
    logger.error(`Failed to update last login timestamp`, { accountId, error });
  }
}
