import { accountModel } from '../../models';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

export const writeNewAccount = async (name: string, email: string, hashedPassword: string) => {
  try {
    const newAccount = await accountModel.create({
      name,
      email,
      password: hashedPassword,
    });

    const accountData = {
      id: newAccount.dataValues.id,
      name: newAccount.dataValues.name,
      email: newAccount.dataValues.email,
      isEmailVerified: newAccount.dataValues.is_email_verified,
    };

    logger.info('New account created successfully', { email, accountId: accountData.id });

    return {
      success: true,
      message: `${Var.app.emoji.success} New account created`,
      payload: accountData,
    };
  } catch (error) {
    logger.error('Failed to create new account', { email, error });
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not create account. Please contact ${Var.app.contact.email}`,
      payload: error,
    };
  }
};
