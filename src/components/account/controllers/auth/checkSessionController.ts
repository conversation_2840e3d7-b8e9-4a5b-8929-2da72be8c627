import { Request, Response } from 'express';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

export const checkSessionController = (req: Request, res: Response) => {
  logger.debug('Session check performed', {
    sessionId: req.session?.id ? req.session.id.substring(0, 5) + '...' : 'none',
    accountId: req.session?.accountId ? req.session.accountId.substring(0, 5) + '...' : 'none',
  });

  return res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} Session active`,
    payload: {
      isSessionActive: true,
    },
  });
};
