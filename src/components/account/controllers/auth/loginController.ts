import { Request, Response, NextFunction } from 'express';
import { readAccountByEmail, writeAccountLockStatus } from '../../dals';
import { verifyPasswordHash } from '../../helpers';
import { Var } from '../../../../global/var';
import { logger, redisClient } from '../../../../global/services';
import { AppError } from '../../../../global/middlewares/handlers/HandleErrors';
import { GenerateCsrfToken } from '../../../security/middlewares/CsrfUtils';

const secureSessionRegeneration = async (req: Request, accountId: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    req.session.regenerate(err => {
      if (err) {
        logger.error('Session regeneration failed:', err);
        return reject(new AppError('Session regeneration failed', 500));
      }

      req.session.accountId = accountId;
      req.session.createdAt = Date.now();
      req.session.userAgent = req.headers['user-agent'];
      req.session.ipAddress = req.ip;

      logger.debug('Session regenerated successfully', {
        accountId: accountId.substring(0, 8) + '...',
        newSessionId: req.session.id?.substring(0, 8) + '...',
      });

      resolve();
    });
  });
};

export const loginController = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { email, password } = res.locals;
    const loginAttemptKey = res.locals.loginAttemptKey;
    const loginAttempts = res.locals.loginAttempts || 0;

    const account: any = await readAccountByEmail(email);
    if (!account || !account.dataValues.password) {
      await incrementFailedLoginAttempts(loginAttemptKey);
      logger.warn(`Failed login attempt for email: ${email}`, {
        reason: 'Account not found or invalid credentials',
      });
      return res.status(401).json({
        success: false,
        message: `${Var.app.emoji.failure} Invalid email or password`,
      });
    }

    if (account.dataValues.is_locked) {
      const lockExpiryTime = new Date(account.dataValues.lock_expires_at);
      if (lockExpiryTime > new Date()) {
        logger.warn(`Login attempt on locked account: ${email}`);
        return res.status(401).json({
          success: false,
          message: `${Var.app.emoji.failure} Account is temporarily locked due to too many failed login attempts. Please try again later or reset your password.`,
        });
      }
    }

    const isPasswordValid: boolean = await verifyPasswordHash(account.dataValues.password, password);

    if (!isPasswordValid) {
      await incrementFailedLoginAttempts(loginAttemptKey);
      if (loginAttempts + 1 >= 10) {
        await lockAccount(account.dataValues.id);
        logger.warn(`Account locked due to too many failed login attempts: ${email}`);
        return res.status(401).json({
          success: false,
          message: `${Var.app.emoji.failure} Account temporarily locked due to too many failed login attempts. Please try again later or reset your password.`,
        });
      }

      logger.warn(`Failed login attempt for email: ${email}`, {
        reason: 'Invalid password',
      });
      return res.status(401).json({
        success: false,
        message: `${Var.app.emoji.failure} Invalid email or password`,
      });
    }

    await redisClient.del(loginAttemptKey);

    if (account.dataValues.is_locked) {
      await unlockAccount(account.dataValues.id);
    }

    logger.info(`Successful login for user: ${email}`);

    // SECURITY FIX: Secure session regeneration to prevent session fixation
    await secureSessionRegeneration(req, account.dataValues.id);

    // Regenerate the CSRF token. This sets a new CSRF cookie on the response.
    // The token value is sent in a custom header for the client to use in subsequent requests.
    const csrfToken = GenerateCsrfToken(req, res);
    res.setHeader('X-CSRF-Token', csrfToken);

    res.locals.accountId = account.dataValues.id;

    const responseData = {
      name: account.dataValues.name,
      email: account.dataValues.email,
      isEmailVerified: account.dataValues.is_email_verified,
      isSessionActive: true,
      lastLogin: new Date().toISOString(),
    };

    return res.status(200).json({
      success: true,
      message: `${Var.app.emoji.success} Logged in successfully`,
      payload: responseData,
    });
  } catch (error) {
    logger.error('Login error', error);
    return next(new AppError('An error occurred during login', 500));
  }
};

async function incrementFailedLoginAttempts(key: string): Promise<void> {
  try {
    await redisClient.incr(key);
    await redisClient.expire(key, 60 * 60);
  } catch (error) {
    logger.error('Error incrementing failed login attempts', error);
  }
}

async function lockAccount(accountId: string): Promise<void> {
  try {
    const lockExpiryTime = new Date(Date.now() + 30 * 60 * 1000);
    await writeAccountLockStatus(accountId, true, lockExpiryTime);
  } catch (error) {
    logger.error('Error locking account', error);
  }
}

async function unlockAccount(accountId: string): Promise<void> {
  try {
    await writeAccountLockStatus(accountId, false, null);
  } catch (error) {
    logger.error('Error unlocking account', error);
  }
}
