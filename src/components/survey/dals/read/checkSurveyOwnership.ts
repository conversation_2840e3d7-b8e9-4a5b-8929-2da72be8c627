import { surveyModel } from '../../models';
import { logger } from '../../../../global/services';

export const checkSurveyOwnership = async (surveyId: string, accountId: string): Promise<boolean> => {
  try {
    const survey = await surveyModel.findOne({
      attributes: ['id'],
      where: {
        id: surveyId,
        account_id: accountId,
        is_deleted: false,
      },
    });

    return !!survey;
  } catch (error) {
    logger.error('Error checking survey ownership', { surveyId, accountId, error });
    return false;
  }
};
