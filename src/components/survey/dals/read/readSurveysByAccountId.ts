import { surveyModel } from '../../models';

export const readSurveysByAccountId = async (accountId: string) => {
  const notes = await surveyModel.findAll({
    attributes: ['id', 'type', 'title', 'tags', 'response_count', 'created_at', 'embed_url', 'share_key', 'public_key', 'meta'],
    where: {
      account_id: accountId,
      is_deleted: false,
    },
    order: [['updated_at', 'DESC']],
  });

  return notes;
};
