import { Request, Response, NextFunction } from 'express';

import { writeNewSurvey } from '../../dals';
import { logger } from '../../../../global/services';
import { Var } from '../../../../global/var';

export const createSurveyController = async (_req: Request, res: Response, next: NextFunction) => {
  const { type, title, distribution, embedUrl, tags, config, respondentDetails } = res.locals;

  const newSurvey: any = await writeNewSurvey(type, title, distribution, embedUrl, tags, config, respondentDetails, res.locals.accountId);

  if (!newSurvey.success) {
    return res.status(400).json({
      success: false,
      message: newSurvey.message,
    });
  }

  logger.info(newSurvey.message);

  const responseData = {
    id: newSurvey.payload.id,
    type: newSurvey.payload.type,
    title: newSurvey.payload.title,
    distribution: newSurvey.payload.distribution,
    embed_url: newSurvey.payload.embed_url,
    share_url: newSurvey.payload.share_url,
    tags: newSurvey.payload.tags,
    config: newSurvey.payload.config,
    respondentDetails: newSurvey.payload.respondentDetails,
  };

  // Store created survey for caching
  res.locals.updatedSurvey = newSurvey.payload;

  res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} New survey created`,
    payload: responseData,
  });

  // Continue to cache middleware
  next();
};
