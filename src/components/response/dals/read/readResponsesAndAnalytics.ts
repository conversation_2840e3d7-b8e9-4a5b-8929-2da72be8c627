import { responseModel } from '../../../response/models';
import { logger } from '../../../../global/services';
import { Var } from '../../../../global/var';

export const readResponsesAndAnalytics = async (surveyId: string, accountId: string) => {
  try {
    const responses = await responseModel.findAll({
      where: {
        survey_id: surveyId,
        account_id: accountId,
        is_discarded: false,
      },
      attributes: ['id', 'response_data', 'created_at'],
      order: [['created_at', 'DESC']],
    });

    return {
      success: true,
      message: `${Var.app.emoji.success} Responses retrieved`,
      payload: responses,
    };
  } catch (error) {
    logger.error('Error retrieving responses:', error);
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not retrieve responses`,
      payload: error,
    };
  }
};
