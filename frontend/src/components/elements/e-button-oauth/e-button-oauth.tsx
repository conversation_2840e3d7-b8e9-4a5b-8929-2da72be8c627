import {
  Component,
  Event,
  EventEmitter,
  FunctionalComponent,
  Prop,
  h,
} from "@stencil/core";
import { Var } from "../../../global/script/var";
import { Store } from "../../../global/script/store";

@Component({
  tag: "e-button-oauth",
  styleUrl: "e-button-oauth.css",
  shadow: true,
})
export class EButtonOauth {
  @Event({
    eventName: "routeToEvent",
    bubbles: true,
  })
  routeToEvent: EventEmitter;

  @Prop() variant: string = "google";

  googleOauthButtonEl!: HTMLDivElement;
  private window: any;

  componentDidLoad() {
    this.window = window;
    if (this.variant === "google") {
      this.initGoogleOauth();
    }
  }

  loadScript(src) {
    return new Promise((resolve) => {
      const script = document.createElement("script");
      script.src = src;
      script.onload = () => {
        resolve(true);
      };
      script.onerror = () => {
        resolve(false);
      };
      document.body.appendChild(script);
    });
  }

  async initGoogleOauth() {
    const isScriptLoaded = await this.loadScript(
      "https://accounts.google.com/gsi/client"
    );
    if (!isScriptLoaded) {
      console.log("Failed to load GSI client");
      return;
    }

    await this.window.google.accounts.id.initialize({
      client_id: Var.keys.oauth.google.clientId,
      callback: (response) => {
        Store.oauthToken = response.credential;
        this.routeToEvent.emit({
          route: "/auth/callback/google",
        });
      },
    });

    await this.window.google.accounts.id.renderButton(
      this.googleOauthButtonEl,
      {
        type: "standard",
        theme: "outline",
        size: "large",
        width: 250,
      }
    );
  }

  GoogleOauthButton: FunctionalComponent = () => (
    <div
      id="button__oauth--google"
      ref={(el) => (this.googleOauthButtonEl = el as HTMLDivElement)}
    ></div>
  );

  render() {
    if (this.variant === "google") {
      return <this.GoogleOauthButton></this.GoogleOauthButton>;
    }
  }
}
