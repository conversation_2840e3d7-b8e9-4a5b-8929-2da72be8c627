import { Component, Prop, Event, EventEmitter, h } from "@stencil/core";

@Component({
  tag: "e-select",
  styleUrl: "e-select.css",
  shadow: true,
})
export class ESelect {
  @Event({
    eventName: "selectChangeEvent",
    bubbles: true,
  })
  selectChangeEventEmitter: EventEmitter;

  @Prop() options: any;
  @Prop() name: string;

  private parsedOptions: any;

  componentWillLoad() {
    this.parseOptionsString();
    this.init();
  }

  parseOptionsString() {
    this.parsedOptions = JSON.parse(this.options);
  }

  init() {
    this.selectChangeEventEmitter.emit({
      name: this.name,
      value: this.parsedOptions[0].value.trim(),
    });
  }

  handleSelectChange(e) {
    this.selectChangeEventEmitter.emit({
      name: this.name,
      value: e.target.value.trim(),
      label: e.target.options[e.target.selectedIndex].text,
    });
  }

  render() {
    return (
      <select onChange={(e) => this.handleSelectChange(e)}>
        {this.parsedOptions.map((option) => (
          <option value={option.value}>{option.label}</option>
        ))}
      </select>
    );
  }
}
