import { Component, h, Prop, State, Watch } from '@stencil/core';
import { RateLimitHandler } from '../../../global/script/helpers';

@Component({
  tag: 'p-rate-limit-notification',
  styleUrl: 'p-rate-limit-notification.css',
  shadow: true,
})
export class PRateLimitNotification {
  @Prop() endpoint: string = '';
  @Prop() show: boolean = false;
  @Prop() autoHide: boolean = true;
  @Prop() hideDelay: number = 5000; // 5 seconds

  @State() timeRemaining: number = 0;
  @State() isVisible: boolean = false;

  private countdownInterval?: number;
  private hideTimeout?: number;

  @Watch('show')
  watchShow(newValue: boolean) {
    if (newValue) {
      this.showNotification();
    } else {
      this.hideNotification();
    }
  }

  @Watch('endpoint')
  watchEndpoint() {
    this.updateTimeRemaining();
  }

  componentDidLoad() {
    if (this.show) {
      this.showNotification();
    }
  }

  disconnectedCallback() {
    this.clearTimers();
  }

  private showNotification() {
    this.isVisible = true;
    this.updateTimeRemaining();
    this.startCountdown();

    if (this.autoHide) {
      this.hideTimeout = window.setTimeout(() => {
        this.hideNotification();
      }, this.hideDelay);
    }
  }

  private hideNotification() {
    this.isVisible = false;
    this.clearTimers();
  }

  private updateTimeRemaining() {
    if (this.endpoint) {
      this.timeRemaining = RateLimitHandler.getTimeUntilReset(this.endpoint);
    }
  }

  private startCountdown() {
    this.clearTimers();
    
    this.countdownInterval = window.setInterval(() => {
      this.updateTimeRemaining();
      
      if (this.timeRemaining <= 0) {
        this.hideNotification();
      }
    }, 1000);
  }

  private clearTimers() {
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
      this.countdownInterval = undefined;
    }
    
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = undefined;
    }
  }

  private formatTime(seconds: number): string {
    if (seconds <= 0) return '0s';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      return `${remainingSeconds}s`;
    }
  }

  private getMessage(): string {
    if (this.endpoint) {
      return RateLimitHandler.getRateLimitMessage(this.endpoint);
    }
    return 'Rate limit exceeded. Please try again later.';
  }

  private handleDismiss = () => {
    this.hideNotification();
  };

  render() {
    if (!this.isVisible) {
      return null;
    }

    return (
      <div class="rate-limit-notification">
        <div class="notification-content">
          <div class="icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12,6 12,12 16,14"></polyline>
            </svg>
          </div>
          
          <div class="message">
            <div class="title">Rate Limit Exceeded</div>
            <div class="description">{this.getMessage()}</div>
            {this.timeRemaining > 0 && (
              <div class="countdown">
                Try again in: <strong>{this.formatTime(this.timeRemaining)}</strong>
              </div>
            )}
          </div>
          
          <button class="dismiss-button" onClick={this.handleDismiss}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>
    );
  }
}
