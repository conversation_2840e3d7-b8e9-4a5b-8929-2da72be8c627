.rate-limit-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
  animation: slideIn 0.3s ease-out;
}

.notification-content {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.icon {
  color: #856404;
  flex-shrink: 0;
  margin-top: 2px;
}

.message {
  flex: 1;
  min-width: 0;
}

.title {
  font-weight: 600;
  color: #856404;
  margin-bottom: 4px;
  font-size: 14px;
}

.description {
  color: #856404;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.countdown {
  color: #856404;
  font-size: 12px;
  font-weight: 500;
}

.countdown strong {
  color: #d63031;
}

.dismiss-button {
  background: none;
  border: none;
  color: #856404;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  flex-shrink: 0;
  transition: background-color 0.2s ease;
}

.dismiss-button:hover {
  background-color: rgba(133, 100, 4, 0.1);
}

.dismiss-button:focus {
  outline: 2px solid #856404;
  outline-offset: 2px;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .rate-limit-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .notification-content {
    padding: 12px;
  }
  
  .title {
    font-size: 13px;
  }
  
  .description {
    font-size: 12px;
  }
  
  .countdown {
    font-size: 11px;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .notification-content {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  .icon,
  .title,
  .description,
  .countdown,
  .dismiss-button {
    color: #e2e8f0;
  }
  
  .countdown strong {
    color: #fc8181;
  }
  
  .dismiss-button:hover {
    background-color: rgba(226, 232, 240, 0.1);
  }
  
  .dismiss-button:focus {
    outline-color: #e2e8f0;
  }
}
