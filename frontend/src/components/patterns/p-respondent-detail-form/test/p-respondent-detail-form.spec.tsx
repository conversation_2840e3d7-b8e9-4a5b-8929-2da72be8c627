import { newSpecPage } from '@stencil/core/testing';
import { PRespondentDetailForm } from '../p-respondent-detail-form';

describe('p-respondent-detail-form', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [PRespondentDetailForm],
      html: `<p-respondent-detail-form></p-respondent-detail-form>`,
    });
    expect(page.root).toEqualHtml(`
      <p-respondent-detail-form>
        <mock:shadow-root>
          <div class="respondent-detail-form">
            <e-text>
              <strong>Add a custom respondent detail</strong>
            </e-text>
            <l-spacer value="1"></l-spacer>
            <e-text>
              <strong>Label <span class="mandatory">*</span></strong>
            </e-text>
            <l-spacer value="0.5"></l-spacer>
            <e-input name="detailLabel" placeholder="e.g. Favorite Color" type="text" value=""></e-input>
            <l-spacer value="1.5"></l-spacer>
            <e-text>
              <strong>Input Type <span class="mandatory">*</span></strong>
            </e-text>
            <l-spacer value="0.5"></l-spacer>
            <e-select name="detailInputType"></e-select>
            <l-spacer value="1.5"></l-spacer>
            <e-input checked="false" name="detailRequired" type="checkbox" value="required">Make this field required</e-input>
            <l-spacer value="1.5"></l-spacer>
            <e-text>
              <strong>Placeholder Text</strong>
            </e-text>
            <l-spacer value="0.5"></l-spacer>
            <e-input name="detailPlaceholder" placeholder="e.g. Enter your favorite color" type="text" value=""></e-input>
            <l-spacer value="1.5"></l-spacer>
            <e-text>
              <strong>Default Value</strong>
            </e-text>
            <l-spacer value="0.5"></l-spacer>
            <e-input name="detailDefaultValue" placeholder="e.g. Blue" type="text" value=""></e-input>
            <l-spacer value="2"></l-spacer>
            <e-button action="submitRespondentDetail">Add Respondent Detail</e-button>
          </div>
        </mock:shadow-root>
      </p-respondent-detail-form>
    `);
  });
});
