import { Component, Event, EventEmitter, State, Listen, h } from '@stencil/core';
import {
  RespondentDetailOption,
  SelectOption,
  AcceptedInputTypes,
} from '../../../global/script/var/RespondentDetailsOptions';

/**
 * Component for creating and editing custom respondent details
 */
@Component({
  tag: 'p-respondent-detail-form',
  styleUrl: 'p-respondent-detail-form.css',
  shadow: true,
})
export class PRespondentDetailForm {
  @Event({
    eventName: 'respondentDetailSubmitEvent',
    bubbles: true,
  })
  respondentDetailSubmitEventEmitter: EventEmitter;

  @State() detailLabel: string = '';
  @State() detailInputType: string = 'text';
  @State() detailRequired: boolean = false;
  @State() detailPlaceholder: string = '';
  @State() detailOptions: SelectOption[] = [];
  @State() detailDefaultValue: string = '';
  @State() currentOption: string = '';
  @State() showOptionsSection: boolean = false;
  @State() formErrors: { [key: string]: string } = {};

  @Listen('inputEvent')
  handleInputEvent(event: CustomEvent) {
    const { name, value, isChecked } = event.detail;

    if (name === 'detailLabel') {
      this.detailLabel = value;
      this.validateField('detailLabel');
    } else if (name === 'detailRequired') {
      this.detailRequired = isChecked;
    } else if (name === 'detailPlaceholder') {
      this.detailPlaceholder = value;
    } else if (name === 'detailDefaultValue') {
      this.detailDefaultValue = value;
    } else if (name === 'currentOption') {
      this.currentOption = value;
    }
  }

  @Listen('selectChangeEvent')
  handleSelectChangeEvent(event: CustomEvent) {
    if (event.detail.name === 'detailInputType') {
      this.detailInputType = event.detail.value;
      this.showOptionsSection = ['select', 'radio', 'checkbox'].includes(this.detailInputType);

      // Clear options if switching away from a type that uses options
      if (!this.showOptionsSection) {
        this.detailOptions = [];
      }
    }
  }

  @Listen('buttonClickEvent')
  handleButtonClickEvent(event: CustomEvent) {
    if (event.detail.action === 'addOption' && this.currentOption.trim()) {
      this.addOption();
    } else if (event.detail.action === 'submitRespondentDetail') {
      this.submitRespondentDetail();
    }
  }

  @Listen('listWithDeleteEvent')
  handleListWithDeleteEvent(event: CustomEvent) {
    if (event.detail.name === 'deleteOption') {
      this.removeOption(event.detail.value);
    }
  }

  private addOption() {
    if (!this.currentOption.trim()) return;

    // Create a value from the label (lowercase, replace spaces with underscores)
    const optionValue = this.currentOption
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '');

    // Check if this value already exists
    const optionExists = this.detailOptions.some(option => option.value === optionValue);
    if (optionExists) return;

    const newOption: SelectOption = {
      value: optionValue,
      label: this.currentOption.trim(),
    };

    this.detailOptions = [...this.detailOptions, newOption];
    this.currentOption = '';
    this.validateField('detailOptions');
  }

  private removeOption(value: string) {
    this.detailOptions = this.detailOptions.filter(option => option.value !== value);
    this.validateField('detailOptions');
  }

  private validateField(fieldName: string): boolean {
    switch (fieldName) {
      case 'detailLabel':
        if (!this.detailLabel.trim()) {
          this.formErrors = { ...this.formErrors, detailLabel: 'Label is required' };
          return false;
        } else {
          const { detailLabel, ...rest } = this.formErrors;
          this.formErrors = rest;
          return true;
        }
      case 'detailOptions':
        if (this.showOptionsSection && this.detailOptions.length === 0) {
          this.formErrors = {
            ...this.formErrors,
            detailOptions: 'At least one option is required',
          };
          return false;
        } else {
          const { detailOptions, ...rest } = this.formErrors;
          this.formErrors = rest;
          return true;
        }
      default:
        return true;
    }
  }

  private validateForm(): boolean {
    let isValid = true;

    // Validate label
    if (!this.validateField('detailLabel')) {
      isValid = false;
    }

    // Validate options if applicable
    if (this.showOptionsSection && !this.validateField('detailOptions')) {
      isValid = false;
    }

    return isValid;
  }

  private submitRespondentDetail() {
    if (!this.validateForm()) {
      return;
    }

    // Generate a unique value for the detail based on the label
    const detailValue = this.detailLabel
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '');

    const respondentDetail: RespondentDetailOption = {
      value: detailValue,
      label: this.detailLabel.trim(),
      inputType: this.detailInputType,
      required: this.detailRequired,
      placeholder: this.detailPlaceholder.trim() || undefined,
      options: this.showOptionsSection ? [...this.detailOptions] : undefined,
      defaultValue: this.detailDefaultValue.trim() || undefined,
    };

    this.respondentDetailSubmitEventEmitter.emit({
      detail: respondentDetail,
    });

    // Reset form
    this.resetForm();
  }

  private resetForm() {
    this.detailLabel = '';
    this.detailInputType = 'text';
    this.detailRequired = false;
    this.detailPlaceholder = '';
    this.detailOptions = [];
    this.detailDefaultValue = '';
    this.currentOption = '';
    this.showOptionsSection = false;
    this.formErrors = {};
  }

  private formatOptionsForList() {
    return JSON.stringify(
      this.detailOptions.map(option => ({
        value: option.value,
        label: option.label,
      })),
    );
  }

  render() {
    return (
      <div class="respondent-detail-form">
        <e-text>
          <strong>
            Label <span class="mandatory">*</span>
          </strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-input
          type="text"
          name="detailLabel"
          placeholder="e.g. Favorite Color"
          value={this.detailLabel}
        ></e-input>
        {this.formErrors.detailLabel && (
          <div class="error-message">
            <e-text variant="footnote">{this.formErrors.detailLabel}</e-text>
          </div>
        )}
        <l-spacer value={1.5}></l-spacer>

        {/* Input Type Field */}
        <e-text>
          <strong>
            Input Type <span class="mandatory">*</span>
          </strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-select name="detailInputType" options={JSON.stringify(AcceptedInputTypes)}></e-select>
        <l-spacer value={1.5}></l-spacer>

        {/* Required Field */}
        <e-input
          type="checkbox"
          name="detailRequired"
          value="required"
          checked={this.detailRequired}
        >
          Make this field required
        </e-input>
        <l-spacer value={1.5}></l-spacer>

        {/* Placeholder Field */}
        <e-text>
          <strong>Placeholder Text</strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-input
          type="text"
          name="detailPlaceholder"
          placeholder="e.g. Enter your favorite color"
          value={this.detailPlaceholder}
        ></e-input>
        <l-spacer value={1.5}></l-spacer>

        {/* Options Section (only for select, radio, checkbox) */}
        {this.showOptionsSection && (
          <div>
            <e-text>
              <strong>
                Options <span class="mandatory">*</span>
              </strong>
            </e-text>
            <l-spacer value={0.5}></l-spacer>
            <l-row justifyContent="flex-start">
              <e-input
                type="text"
                name="currentOption"
                placeholder="e.g. Red"
                value={this.currentOption}
              ></e-input>
              <l-spacer variant="horizontal" value={0.5}></l-spacer>
              <e-button variant="ghost" action="addOption" disabled={!this.currentOption.trim()}>
                Add
              </e-button>
            </l-row>
            <l-spacer value={0.5}></l-spacer>
            <p-list-with-delete
              name="deleteOption"
              items={this.formatOptionsForList()}
              emptyMessage="No options added yet"
            ></p-list-with-delete>
            {this.formErrors.detailOptions && (
              <div class="error-message">
                <e-text variant="footnote">{this.formErrors.detailOptions}</e-text>
              </div>
            )}
            <l-spacer value={1.5}></l-spacer>
          </div>
        )}

        {/* Default Value Field */}
        <e-text>
          <strong>Default Value</strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-input
          type="text"
          name="detailDefaultValue"
          placeholder="e.g. Blue"
          value={this.detailDefaultValue}
        ></e-input>
        <l-spacer value={2}></l-spacer>

        {/* Submit Button */}
        <e-button action="submitRespondentDetail">Add Respondent Detail</e-button>
      </div>
    );
  }
}
