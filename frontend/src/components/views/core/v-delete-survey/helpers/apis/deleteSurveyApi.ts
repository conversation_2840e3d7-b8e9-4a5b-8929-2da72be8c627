import { Var } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export const deleteSurveyApi = async (surveyId: string) => {
  try {
    const result = await ApiWrapper(`${Var.api.endpoint.surveys}/${surveyId}`, {
      method: 'DELETE',
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error deleting survey:', error);
    return {
      success: false,
      message: 'Error deleting survey',
      payload: null,
    };
  }
};
