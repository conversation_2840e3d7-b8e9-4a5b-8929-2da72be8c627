import { Component, FunctionalComponent, State, Prop, h } from '@stencil/core';
import { Store } from '../../../../global/script/store';
import { GetSurveyApi } from '../../../../global/script/helpers';

@Component({
  tag: 'v-survey-results',
  styleUrl: 'v-survey-results.css',
  shadow: true,
})
export class VSurveyResults {
  @Prop() surveyId: string;
  @State() compState: string = 'fetching';
  @State() survey: any;

  componentWillLoad() {
    Store.activeView = 'surveyResults';
    document.title = 'Survey Results | Sensefolks';
    this.getSurvey();
  }

  async getSurvey() {
    let { success, message, payload } = await GetSurveyApi(this.surveyId);
    if (!success) {
      this.compState = 'error';
      return alert(message);
    }
    if (!payload) {
      return alert(message);
    }
    this.survey = payload;
    this.compState = 'ready';
  }

  generateSurveyPill(value: string) {
    if (value === 'sensePrice') {
      return <e-pill color="purple">SensePrice</e-pill>;
    } else if (value === 'senseFeature') {
      return <e-pill color="blue">SenseFeature</e-pill>;
    } else if (value === 'sensePoll') {
      return <e-pill color="indigo">SensePoll</e-pill>;
    } else if (value === 'senseQuery') {
      return <e-pill color="turquoise">SenseQuery</e-pill>;
    }
  }

  Fetching: FunctionalComponent = () => (
    <div class="centered">
      <e-spinner theme="dark"></e-spinner>
    </div>
  );

  Ready: FunctionalComponent = () => (
    <div id="ready-container">
      <l-row align="baseline">
        <div id="ready-container__sidebar">
          <e-link url="/">← Back</e-link>
          <l-spacer value={2}></l-spacer>
          <e-button variant="link">Overview</e-button>
          <e-button variant="link">Comments</e-button>
          <e-button variant="link">Respondents</e-button>
        </div>
        <div id="ready-container__main-content">
          <l-row align="flex-start">
            <e-text variant="footnote">{this.generateSurveyPill(this.survey.type)}</e-text>
            {/* <e-text variant="footnote">{this.survey.timestamp.toUpperCase()}</e-text> */}
            <l-row>
              <e-text variant="footnote">
                <e-link url={`/surveys/${this.surveyId}/edit`}>Edit</e-link>
              </e-text>
              <l-spacer variant="horizontal" value={0.5}></l-spacer>
              {(this.survey.distribution === 'embed' ||
                this.survey.distribution === 'embed&link') && (
                <l-row>
                  <e-text variant="footnote">/</e-text>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                  <e-text variant="footnote">
                    <e-link url={`/surveys/${this.surveyId}/embed`}>Embed</e-link>
                  </e-text>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                </l-row>
              )}
              {(this.survey.distribution === 'link' ||
                this.survey.distribution === 'embed&link') && (
                <l-row>
                  <e-text variant="footnote">/</e-text>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                  <e-text variant="footnote">
                    <e-link url={`/surveys/${this.surveyId}/share`}>Share</e-link>
                  </e-text>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                </l-row>
              )}
              <e-text variant="footnote">/</e-text>
              <l-spacer variant="horizontal" value={0.5}></l-spacer>
              <e-text variant="footnote">
                <e-link theme="danger" url={`/surveys/${this.surveyId}/delete`}>
                  Delete
                </e-link>
              </e-text>
            </l-row>
          </l-row>
          <l-spacer value={0.5}></l-spacer>
          <e-text>{this.survey.title}</e-text>
          <l-spacer value={1}></l-spacer>
          <l-separator></l-separator>
        </div>
      </l-row>
    </div>
  );

  Error: FunctionalComponent = () => (
    <div id="error-container">
      <article>
        <e-text variant="display">Could not fetch survey details :(</e-text>
        <l-spacer value={1}></l-spacer>
        <e-text>
          If the issue persists, kindly visit the <e-link url="/support">Support page</e-link> and
          report the issue
        </e-text>
      </article>
    </div>
  );

  render() {
    return (
      <c-main>
        {this.compState === 'fetching' && <this.Fetching></this.Fetching>}
        {this.compState === 'ready' && <this.Ready></this.Ready>}
        {this.compState === 'error' && <this.Error></this.Error>}
      </c-main>
    );
  }
}
