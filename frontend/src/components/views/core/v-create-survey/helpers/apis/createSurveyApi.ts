import { createSurveyPayloadInterface } from '../../interfaces';
import { Var } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export const createSurveyApi = async (payload: createSurveyPayloadInterface) => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.surveys, {
      method: 'POST',
      body: payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error creating survey:', error);
    return {
      success: false,
      message: 'Error creating survey',
      payload: null,
    };
  }
};
