import {
  Component,
  Event,
  EventEmitter,
  State,
  Host,
  Listen,
  h,
} from "@stencil/core";
import {
  loginApi,
  generateLoginPayload,
  validateLoginPayload,
} from "./helpers";
import { loginPayloadInterface } from "./interfaces";
import { Var } from "../../../../global/script/var";
import { Store } from "../../../../global/script/store";

@Component({
  tag: "v-login",
  styleUrl: "v-login.css",
  shadow: true,
})
export class VLogin {
  @Event({
    eventName: "authSuccessfulEvent",
    bubbles: true,
  })
  authSuccessfulEventEmitter: EventEmitter;

  @Listen("buttonClickEvent") handleButtonClickEvent(e) {
    if (e.detail.action === "loginUser") {
      this.loginUser();
    }
  }

  @Listen("inputEvent") handleInputEvent(e) {
    if (e.detail.name === "email") {
      this.email = e.detail.value;
    } else if (e.detail.name === "password") {
      this.password = e.detail.value;
    }
  }

  @State() isLoggingIn: boolean = false;
  private email: string = "";
  private password: string = "";

  async loginUser() {
    let loginPayload: loginPayloadInterface = generateLoginPayload(
      this.email,
      this.password
    );
    let { isValid, validationMessage } = validateLoginPayload(loginPayload);
    if (!isValid) {
      return alert(validationMessage);
    }
    this.isLoggingIn = true;
    let { success, message } = await loginApi(loginPayload);
    this.isLoggingIn = false;
    if (!success) {
      return alert(message);
    }
    this.authSuccessfulEventEmitter.emit();
  }

  componentWillLoad() {
    Store.activeView = "login";
    document.title = "Login | Sensefolks";
  }

  render() {
    return (
      <Host>
        <div class="form">
          <e-image
            src={Var.app.branding.logo.logotype.withoutBg}
            width="180px"
          ></e-image>
          <l-spacer value={1}></l-spacer>
          <p-dotgrid height="50px" width="100%"></p-dotgrid>
          <l-spacer value={2}></l-spacer>
          <e-text variant="display">Log in</e-text>
          <l-spacer value={0.75}></l-spacer>
          <l-row justifyContent="flex-start"></l-row>
          <e-text>
            Don't have an account? <e-link url="/signup">Sign up</e-link>
          </e-text>
          <l-spacer value={2}></l-spacer>
          <e-button-oauth></e-button-oauth>
          <l-spacer value={2.5}></l-spacer>
          <l-separator variant="oauth"></l-separator>
          <l-spacer value={2.5}></l-spacer>
          <e-input type="email" name="email" placeholder="Email"></e-input>
          <l-spacer value={2}></l-spacer>
          <e-input
            type="password"
            name="password"
            placeholder="Password (Min. 8 letters)"
          ></e-input>
          <l-spacer value={2}></l-spacer>
          <e-button action="loginUser" active={this.isLoggingIn} size="wide">
            Log in
          </e-button>
          <l-spacer value={1}></l-spacer>
          <e-link url="/password-reset">Reset password</e-link>
          <l-spacer value={2}></l-spacer>
          <l-separator></l-separator>
          <l-spacer value={1.5}></l-spacer>
          <e-text variant="footnote">
            By logging in, you accept our{" "}
            <e-link variant="externalLink" url={Var.app.policy.tos.url}>
              terms of service
            </e-link>{" "}
            &{" "}
            <e-link variant="externalLink" url={Var.app.policy.privacy.url}>
              privacy policy
            </e-link>
          </e-text>
        </div>
      </Host>
    );
  }
}
