import { loginPayloadInterface } from '../../interfaces';
import { Var } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export const loginApi = async (payload: loginPayloadInterface) => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.account.auth.login, {
      method: 'POST',
      body: payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error during login request:', error);
    return {
      success: false,
      message: 'Error during login request',
      payload: null,
    };
  }
};
