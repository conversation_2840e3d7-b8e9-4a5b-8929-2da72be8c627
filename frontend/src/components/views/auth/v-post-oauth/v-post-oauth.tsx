import {
  Component,
  Prop,
  Event,
  EventEmitter,
  FunctionalComponent,
  State,
  Host,
  h,
} from "@stencil/core";
import { googleProfilePayloadGenerator, googleProfileApi } from "./helpers";
import { googleProfilePayloadInterface } from "./interfaces";
import { Store } from "../../../../global/script/store";

@Component({
  tag: "v-post-oauth",
  styleUrl: "v-post-oauth.css",
  shadow: true,
})
export class VPostOauth {
  @Event({
    eventName: "authSuccessfulEvent",
    bubbles: true,
  })
  authSuccessfulEventEmitter: EventEmitter;

  @Event({
    eventName: "closeModal",
    bubbles: true,
  })
  closeModalEventEmitter: EventEmitter;

  @Event({
    eventName: "routeToEvent",
    bubbles: true,
  })
  routeToEvent: EventEmitter;

  @Prop() provider: string;

  @State() activeView: string = "fetching";

  componentWillLoad() {
    Store.activeView = "postOauth";
    document.title = "Post-Oauth | Sensefolks";
  }

  componentDidLoad() {
    if (this.provider === "google") {
      this.getGoogleProfile(Store.oauthToken);
    }
  }

  async getGoogleProfile(token: string) {
    let googleProfilePayload: googleProfilePayloadInterface =
      googleProfilePayloadGenerator(token);
    let { success } = await googleProfileApi(googleProfilePayload);

    if (!success) {
      this.closeModalEventEmitter.emit();
      this.activeView = "error";
      return;
    }

    this.authSuccessfulEventEmitter.emit();

    setTimeout(() => {
      this.routeToEvent.emit({
        type: "push",
        route: "/",
        data: {},
      });
    }, 1000);
  }

  Loader: FunctionalComponent = () => (
    <l-row align="center">
      <p-spinner></p-spinner>
      <l-spacer value={1}></l-spacer>
      <e-text>Fetching profile info..</e-text>
    </l-row>
  );

  Error: FunctionalComponent = () => (
    <div>
      <e-text theme="danger">
        <strong>Oops..</strong>
      </e-text>
      <e-text>
        We could not fetch your google account details. Please try again
      </e-text>
      <e-text>
        <e-link url="/login">Login </e-link> /{" "}
        <e-link url="/signup">Sign up</e-link>
      </e-text>
    </div>
  );

  render() {
    return (
      <Host>
        <div id="post-oauth-container">
          {this.activeView === "fetching" && <this.Loader></this.Loader>}
          {this.activeView === "error" && <this.Error></this.Error>}
        </div>
      </Host>
    );
  }
}
