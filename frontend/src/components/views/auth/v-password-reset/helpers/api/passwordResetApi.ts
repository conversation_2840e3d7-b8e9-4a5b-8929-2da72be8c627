import { passwordResetPayloadInterface } from '../../interfaces';
import { Var } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export const passwordResetApi = async (payload: passwordResetPayloadInterface) => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.account.password.details, {
      method: 'PUT',
      body: payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error resetting password:', error);
    return {
      success: false,
      message: 'Error resetting password',
      payload: null,
    };
  }
};
