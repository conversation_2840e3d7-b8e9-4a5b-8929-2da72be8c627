import {
  Component,
  Prop,
  Event,
  EventEmitter,
  FunctionalComponent,
  Listen,
  State,
  h,
} from "@stencil/core";
import { Store } from "../../../../global/script/store";

@Component({
  tag: "v-checkout",
  styleUrl: "v-checkout.css",
  shadow: true,
})
export class VCheckout {
  @Event({
    eventName: "routeToEvent",
    bubbles: true,
  })
  routeToEvent: EventEmitter;

  @Prop() orderId: string;

  @State() isViewDataFetched: boolean = false;
  @State() isConfirmAndPayButtonActive: boolean = false;
  @State() isConfirmAndPayButtonDisabled: boolean = true;

  @Listen("buttonClickEvent") handleButtonClickEvent(e) {
    if (e.detail.action === "createCheckoutSession") {
      this.createCheckoutSession();
    } else if (e.detail.action === "goBack") {
      this.routeToEvent.emit({
        type: "goBack",
        data: {},
      });
    }
  }

  createCheckoutSession() {
    console.log("Create checkout session");
  }

  private productName: string = "";
  private subscriptionType: string = "";
  private currency: string = "";
  private price: number = 0;
  private total: number = 0;

  componentWillLoad() {
    Store.activeView = "checkout";
    document.title = "Checkout | Sensefolks";
  }

  componentDidLoad() {
    this.fetchViewData();
  }

  fetchViewData() {}

  Details: FunctionalComponent = () => (
    <div>
      <e-text variant="subHeading">{this.productName}</e-text>
      <e-text>{this.subscriptionType}</e-text>
      <l-spacer value={0.5}></l-spacer>
      <l-separator></l-separator>
    </div>
  );

  Skel: FunctionalComponent = () => (
    <div>
      <l-spacer value={1}></l-spacer>
      <div class="skel__line"></div>
      <l-spacer value={1}></l-spacer>
      <div class="skel__line"></div>
      <l-spacer value={1}></l-spacer>
      <div class="skel__line"></div>
      <l-spacer value={1}></l-spacer>
    </div>
  );

  Summary: FunctionalComponent = () => (
    <div>
      {" "}
      <table>
        <tr>
          <td>Item cost</td>
          <td>
            {this.currency}
            {this.price}
          </td>
        </tr>
        <tr>
          <td>
            <strong>Grand total</strong>
          </td>
          <td>
            <strong>
              {this.currency}
              {this.total}
            </strong>
          </td>
        </tr>
      </table>
    </div>
  );

  render() {
    return (
      <c-main>
        <c-card>
          <e-text variant="heading">Checkout</e-text>
          <l-spacer value={1.5}></l-spacer>
          <l-separator></l-separator>
          <l-spacer value={2.5}></l-spacer>
          {this.isViewDataFetched ? (
            <div>
              <this.Details></this.Details>
              <l-spacer value={0.25}></l-spacer>
              <this.Summary></this.Summary>
              <l-spacer value={0.5}></l-spacer>
              <e-text variant="footnote">Item cost includes GST</e-text>
            </div>
          ) : (
            <div>
              <this.Skel></this.Skel>
              <l-spacer value={2}></l-spacer>
              <this.Skel></this.Skel>
            </div>
          )}
          <l-spacer value={2}></l-spacer>
          <l-row justifyContent="space-between">
            <e-link url="../">Back</e-link>
            <e-button
              action="createCheckoutSession"
              disabled={this.isConfirmAndPayButtonDisabled}
              active={this.isConfirmAndPayButtonActive}
            >
              Confirm & pay
            </e-button>
          </l-row>
        </c-card>
      </c-main>
    );
  }
}
