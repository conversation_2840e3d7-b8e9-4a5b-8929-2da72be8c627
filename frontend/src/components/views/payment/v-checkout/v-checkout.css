c-card {
  width: 90%;
  max-width: 400px;
}

input[type="radio"]:hover {
  cursor: pointer;
}

table {
  border-collapse: collapse;
  width: 100%;
  border-radius: 0.5em;
}

table,
td {
  border: 1px solid var(--color__grey--200);
  text-align: left;
}

th,
td {
  padding: 0.5em 0.5em;
}

.skel__line {
  height: 10px;
  border-radius: 0.25em;
  background: #ccc;
  background-image: linear-gradient(
    90deg,
    #f4f4f4 0px,
    rgba(229, 229, 229, 0.8) 40%,
    #f4f4f4 100%
  );
  background-size: 200%;
  animation: skel-shine 1s infinite ease-out;
}

@keyframes skel-shine {
  0% {
    background-position: -100px;
  }
  40%,
  100% {
    background-position: 140px;
  }
}
