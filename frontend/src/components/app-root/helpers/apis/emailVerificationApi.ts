import { emailVerificationPayloadInterface } from '../../interfaces';
import { Var } from '../../../../global/script/var';
import { ConstructApiUrl } from '../../../../global/script/helpers';

export const emailVerificationApi = async (payload: emailVerificationPayloadInterface) => {
  let url: string = ConstructApiUrl(Var.api.endpoint.account.email.verify);
  let options: any = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(payload),
  };

  let returnData: any;
  await fetch(url, options)
    .then(response => response.json())
    .then(data => {
      returnData = data;
    })
    .catch(error => {
      console.log(error);
    });

  return {
    success: returnData.success,
    message: returnData.message,
    payload: returnData.payload,
  };
};
