export {
  RespondentDetailsOptions,
  RespondentDetailOption,
  SelectOption,
  AcceptedInputTypes,
} from './RespondentDetailsOptions';

export const Var = {
  app: {
    name: 'Sensefolks',
    contact: {
      email: '<EMAIL>',
      url: 'https://sensefolks.com/support',
    },
    domain: 'sensefolks.com',
    branding: {
      logo: {
        logomark: {
          withoutBg:
            'https://res.cloudinary.com/dyygc6dx2/image/upload/v1743454433/logomark_eukmle.svg',
          withBg:
            'https://res.cloudinary.com/dyygc6dx2/image/upload/v1743454443/logomark_with_bg_tzpppc.svg',
        },
        logotype: {
          withBg:
            'https://res.cloudinary.com/dyygc6dx2/image/upload/v1743456084/logotype_with_bg_n9t098.svg',
          withoutBg:
            'https://res.cloudinary.com/dyygc6dx2/image/upload/v1743453235/logotype_pxbcf5.svg',
        },
      },
    },
    policy: {
      tos: {
        url: 'https://sensefolks.com/policies/terms-of-service',
      },
      privacy: {
        url: 'https://sensefolks.com/policies/privacy-policy',
      },
      cancellationAndRefund: {
        url: 'https://sensefolks.com/policies/cancellation-and-refund',
      },
    },
    url: 'https://sensefolks.com',
    owner: {
      name: 'Projckt',
      website: { url: 'https://projckt.com' },
      contact: {
        address: 'Guwahati, Assam, India',
        email: '<EMAIL>',
      },
    },
  },
  api: {
    url: document.domain === 'localhost' ? 'http://localhost:4444' : 'https://api.sensefolks.com',
    version: 'v1',
    endpoint: {
      csrf: '/csrf-token',
      account: {
        details: '/account',
        auth: {
          login: '/auth/login',
          logout: '/auth/logout',
          signup: '/auth/signup',
          sessionCheck: '/auth/session-check',
          oauth: {
            google: '/auth/google',
          },
        },
        email: {
          sendVerificationCode: '/account/email/send-code',
          verify: '/account/email/verify',
        },
        password: {
          details: '/account/password',
          sendResetCode: '/account/password/send-code',
          reset: '/account/password/reset',
        },
      },
      surveys: '/surveys',
    },
  },
  keys: {
    oauth: {
      google: {
        clientId: '************-uqsfujfmi72a80r7u6d4bnhpdrepo7qf.apps.googleusercontent.com',
      },
    },
  },
};
