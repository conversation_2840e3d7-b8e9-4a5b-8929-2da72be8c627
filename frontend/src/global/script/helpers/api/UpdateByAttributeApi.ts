import { ApiWrapper } from './ApiWrapper';

export const UpdateByAttributeApi = async (payload: any, fullUrl: string) => {
  try {
    // Extract endpoint from full URL for ApiWrapper
    const urlObj = new URL(fullUrl);
    const endpoint = urlObj.pathname.replace('/v1', ''); // Remove version prefix

    const result = await ApiWrapper(endpoint, {
      method: 'PUT',
      body: payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error in UpdateByAttributeApi:', error);
    return {
      success: false,
      message: 'Error updating data',
      payload: null,
    };
  }
};
