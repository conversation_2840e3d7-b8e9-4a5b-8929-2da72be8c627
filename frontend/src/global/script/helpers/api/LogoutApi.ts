import { Var } from '../../var';
import { ApiWrapper } from './ApiWrapper';
import { Store } from '../../store';

export const LogoutApi = async () => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.account.auth.logout, {
      method: 'POST',
    });

    // Clear CSRF token regardless of result since logout destroys session
    Store.csrfToken = '';
    console.debug('CSRF token cleared after logout attempt');

    // If logout failed due to CSRF but we're already logged out, treat as success
    if (!result.success && result.message?.includes('Invalid security token')) {
      console.warn(
        'Logout failed due to CSRF token, but treating as success since session is likely invalid',
      );
      return {
        success: true,
        message: 'Logout successful (session was already invalid)',
        payload: { isSessionActive: false },
      };
    }

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error during logout request:', error);
    // Clear CSRF token even on error since we're logging out
    Store.csrfToken = '';
    return {
      success: false,
      message: 'Error during logout request',
      payload: null,
    };
  }
};
