import { MailPayloadInterface } from '../../interfaces';
import { Var } from '../../var';
import { ApiWrapper } from './ApiWrapper';

export const MailApi = async (payload: MailPayloadInterface) => {
  let endpoint: string = '';
  if (payload.variant === 'emailVerificationCode') {
    endpoint = Var.api.endpoint.account.email.sendVerificationCode;
  } else if (payload.variant === 'passwordResetCode') {
    endpoint = Var.api.endpoint.account.password.sendResetCode;
  } else {
    return {
      success: false,
      message: 'Invalid mail variant',
      payload: null,
    };
  }

  try {
    const result = await <PERSON><PERSON><PERSON>rapper(endpoint, {
      method: 'POST',
      body: payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error in MailApi:', error);
    return {
      success: false,
      message: 'Error sending email',
      payload: null,
    };
  }
};
