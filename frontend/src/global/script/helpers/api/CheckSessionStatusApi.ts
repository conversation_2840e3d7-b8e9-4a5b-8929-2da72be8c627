import { Var } from '../../var';
import { ApiWrapper } from './ApiWrapper';

export const CheckSessionStatusApi = async () => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.account.auth.sessionCheck, {
      method: 'GET',
      includeCsrf: true, // Include CSRF token for consistency
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error checking session status:', error);
    return {
      success: false,
      message: 'Failed to check session status',
      payload: null,
    };
  }
};
