import { Var } from '../../var';
import { ApiWrapper } from './ApiWrapper';

export const GetSurveyApi = async (surveyId: string) => {
  try {
    const result = await ApiWrapper(`${Var.api.endpoint.surveys}/${surveyId}`, {
      method: 'GET',
      includeCsrf: true, // Include CSRF token for authenticated requests
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error fetching survey:', error);
    return {
      success: false,
      message: 'Error fetching survey',
      payload: null,
    };
  }
};
