import { Var } from '../../var';
import { Store } from '../../store';
import { ApiWrapper } from './ApiWrapper';

export const GetCsrfTokenApi = async (): Promise<{ success: boolean; message: string }> => {
  try {
    const result = await <PERSON><PERSON>Wrapper(Var.api.endpoint.csrf, {
      method: 'GET',
      includeCsrf: false, // Don't include CSRF token when fetching it
    });

    // Check if token was updated by <PERSON>pi<PERSON><PERSON><PERSON>
    if (Store.csrfToken) {
      return {
        success: true,
        message: 'CSRF token fetched successfully',
      };
    }

    // Fallback: check response payload for token (backward compatibility)
    if (result.success && result.payload && result.payload.token) {
      Store.csrfToken = result.payload.token;
      console.debug(
        'CSRF token fetched from response body:',
        result.payload.token.substring(0, 10) + '...',
      );
      return {
        success: true,
        message: 'CSRF token fetched successfully from response body',
      };
    }

    console.error('Failed to fetch CSRF token: No token in response headers or body');
    return {
      success: false,
      message: result.message || 'No CSRF token found in response',
    };
  } catch (error) {
    console.error('Error fetching CSRF token:', error);
    return {
      success: false,
      message: 'Error fetching CSRF token',
    };
  }
};
