import { Store } from '../../store';

/**
 * CSRF Token Validator utility
 * Provides methods to validate and refresh CSRF tokens
 */
export class CsrfValidator {
  /**
   * Check if the current CSRF token is valid (non-empty)
   */
  static isTokenValid(): boolean {
    return !!Store.csrfToken && Store.csrfToken.length > 0;
  }

  /**
   * Refresh the CSRF token if it's invalid or missing
   * Note: This method should be called from outside to avoid circular dependencies
   */
  static async ensureValidToken(
    fetchTokenFn?: () => Promise<{ success: boolean; message: string }>,
  ): Promise<boolean> {
    if (this.isTokenValid()) {
      return true;
    }

    if (!fetchTokenFn) {
      console.warn('CSRF token missing and no fetch function provided');
      return false;
    }

    console.debug('CSRF token missing or invalid, fetching new token...');
    const result = await fetchTokenFn();

    if (result.success) {
      console.debug('CSRF token refreshed successfully');
      return true;
    } else {
      console.error('Failed to refresh CSRF token:', result.message);
      return false;
    }
  }

  /**
   * Clear the current CSRF token (useful for logout or error scenarios)
   */
  static clearToken(): void {
    Store.csrfToken = '';
    console.debug('CSRF token cleared');
  }

  /**
   * Get token info for debugging
   */
  static getTokenInfo(): { hasToken: boolean; tokenPrefix: string; tokenLength: number } {
    const hasToken = this.isTokenValid();
    return {
      hasToken,
      tokenPrefix: hasToken ? Store.csrfToken.substring(0, 10) + '...' : 'none',
      tokenLength: Store.csrfToken.length,
    };
  }

  /**
   * Validate token format (basic check for expected length and characters)
   */
  static isTokenFormatValid(): boolean {
    if (!Store.csrfToken) return false;

    // Basic validation: should be a reasonable length and contain valid characters
    const token = Store.csrfToken;
    const isValidLength = token.length >= 32 && token.length <= 256;
    const isValidFormat = /^[a-zA-Z0-9+/=_-]+$/.test(token);

    return isValidLength && isValidFormat;
  }
}
